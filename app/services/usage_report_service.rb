# frozen_string_literal: true

class UsageReportService < ApplicationService
  attr_reader :tenant, :report_type, :comparison_type, :date_range, :start_date, :end_date

  def initialize(tenant:, report_type:, comparison_type: 'none', date_range: '30_days', start_date: nil, end_date: nil)
    @tenant = tenant
    @report_type = report_type
    @comparison_type = comparison_type
    @date_range = date_range
    @start_date = start_date&.to_date
    @end_date = end_date&.to_date
  end

  def call
    generate_report
  end

  def generate_report
    case comparison_type
    when 'none'
      generate_single_period_report
    when 'yesterday_today'
      generate_comparison_report(Date.current - 1.day, Date.current, Date.current - 1.day, Date.current)
    when 'last_week_this_week'
      generate_comparison_report(1.week.ago.beginning_of_week, 1.week.ago.end_of_week, 
                                Date.current.beginning_of_week, Date.current.end_of_week)
    when 'last_month_this_month'
      generate_comparison_report(1.month.ago.beginning_of_month, 1.month.ago.end_of_month,
                                Date.current.beginning_of_month, Date.current.end_of_month)
    when 'last_quarter_this_quarter'
      generate_comparison_report(3.months.ago.beginning_of_quarter, 3.months.ago.end_of_quarter,
                                Date.current.beginning_of_quarter, Date.current.end_of_quarter)
    when 'last_year_this_year'
      generate_comparison_report(1.year.ago.beginning_of_year, 1.year.ago.end_of_year,
                                Date.current.beginning_of_year, Date.current.end_of_year)
    when 'custom'
      if start_date && end_date
        generate_single_period_report(start_date, end_date)
      else
        { error: 'Custom date range requires start_date and end_date' }
      end
    else
      generate_single_period_report
    end
  end

  private

  def generate_single_period_report(custom_start = nil, custom_end = nil)
    period_start, period_end = determine_date_range(custom_start, custom_end)
    
    data = case report_type
           when 'daily_logged_in_users'
             daily_logged_in_users_report(period_start, period_end)
           when 'daily_lead_creation'
             daily_metric_report('created_lead_count', period_start, period_end)
           when 'daily_deal_creation'
             daily_metric_report('created_deal_count', period_start, period_end)
           when 'daily_contact_creation'
             daily_metric_report('created_contact_count', period_start, period_end)
           when 'daily_task_creation'
             daily_metric_report('created_task_count', period_start, period_end)
           when 'daily_meeting_creation'
             daily_metric_report('created_meeting_count', period_start, period_end)
           when 'daily_note_creation'
             daily_metric_report('created_note_count', period_start, period_end)
           when 'daily_company_creation'
             daily_metric_report('created_company_count', period_start, period_end)
           when 'daily_quote_creation'
             daily_metric_report('created_quote_count', period_start, period_end)
           when 'daily_calls_logged'
             daily_metric_report('calls_logged', period_start, period_end)
           when 'daily_emails_sent'
             daily_metric_report('emails_sent', period_start, period_end)
           when 'user_activity_summary'
             user_activity_summary_report(period_start, period_end)
           when 'feature_usage_overview'
             feature_usage_overview_report(period_start, period_end)
           when 'active_inactive_users'
             active_inactive_users_report(period_start, period_end)
           when 'email_integration_usage'
             integration_usage_report('email_account_connected', period_start, period_end)
           when 'calendar_integration_usage'
             integration_usage_report('calendar_account_connected', period_start, period_end)
           when 'marketplace_apps_usage'
             marketplace_apps_usage_report(period_start, period_end)
           else
             { error: 'Invalid report type' }
           end

    {
      report_type: report_type,
      comparison_type: comparison_type,
      period: { start: period_start, end: period_end },
      data: data,
      generated_at: Time.current
    }
  end

  def generate_comparison_report(period1_start, period1_end, period2_start, period2_end)
    period1_data = generate_single_period_report(period1_start, period1_end)
    period2_data = generate_single_period_report(period2_start, period2_end)

    {
      report_type: report_type,
      comparison_type: comparison_type,
      period1: { start: period1_start, end: period1_end, data: period1_data[:data] },
      period2: { start: period2_start, end: period2_end, data: period2_data[:data] },
      comparison: calculate_comparison(period1_data[:data], period2_data[:data]),
      generated_at: Time.current
    }
  end

  def determine_date_range(custom_start = nil, custom_end = nil)
    return [custom_start, custom_end] if custom_start && custom_end

    case date_range
    when '7_days'
      [7.days.ago.to_date, Date.current]
    when '30_days'
      [30.days.ago.to_date, Date.current]
    when '90_days'
      [90.days.ago.to_date, Date.current]
    when '6_months'
      [6.months.ago.to_date, Date.current]
    when '1_year'
      [1.year.ago.to_date, Date.current]
    else
      [30.days.ago.to_date, Date.current]
    end
  end

  def base_usage_query(start_date, end_date)
    tenant.usage_histories
          .where(date: start_date..end_date)
          .where.not(date: nil)
  end

  def daily_logged_in_users_report(start_date, end_date)
    base_usage_query(start_date, end_date)
      .where(logged_in: true)
      .group(:date)
      .count
      .transform_keys { |date| date.strftime('%Y-%m-%d') }
  end

  def daily_metric_report(metric_column, start_date, end_date)
    base_usage_query(start_date, end_date)
      .group(:date)
      .sum(metric_column)
      .transform_keys { |date| date.strftime('%Y-%m-%d') }
  end

  def user_activity_summary_report(start_date, end_date)
    query = base_usage_query(start_date, end_date)
    
    {
      total_users: query.distinct.count(:user_id),
      active_users: query.where(active: true).distinct.count(:user_id),
      daily_active_users: query.where(dau: true).distinct.count(:user_id),
      verified_users: query.where(verified: true).distinct.count(:user_id),
      users_with_email_connected: query.where(email_account_connected: true).distinct.count(:user_id),
      users_with_calendar_connected: query.where(calendar_account_connected: true).distinct.count(:user_id)
    }
  end

  def feature_usage_overview_report(start_date, end_date)
    query = base_usage_query(start_date, end_date)
    
    {
      total_leads_created: query.sum(:created_lead_count),
      total_deals_created: query.sum(:created_deal_count),
      total_contacts_created: query.sum(:created_contact_count),
      total_tasks_created: query.sum(:created_task_count),
      total_meetings_created: query.sum(:created_meeting_count),
      total_notes_created: query.sum(:created_note_count),
      total_companies_created: query.sum(:created_company_count),
      total_quotes_created: query.sum(:created_quote_count),
      total_calls_logged: query.sum(:calls_logged),
      total_emails_sent: query.sum(:emails_sent),
      total_custom_dashboards: query.sum(:number_of_custom_dashboards_created),
      total_marketplace_apps: query.sum(:number_of_marketplace_apps_installed)
    }
  end

  def active_inactive_users_report(start_date, end_date)
    query = base_usage_query(start_date, end_date)
    
    {
      active_users_by_date: query.where(active: true).group(:date).count.transform_keys { |date| date.strftime('%Y-%m-%d') },
      inactive_users_by_date: query.where(active: false).group(:date).count.transform_keys { |date| date.strftime('%Y-%m-%d') },
      dau_by_date: query.where(dau: true).group(:date).count.transform_keys { |date| date.strftime('%Y-%m-%d') }
    }
  end

  def integration_usage_report(integration_field, start_date, end_date)
    query = base_usage_query(start_date, end_date)
    
    {
      connected_by_date: query.where(integration_field => true).group(:date).count.transform_keys { |date| date.strftime('%Y-%m-%d') },
      not_connected_by_date: query.where(integration_field => false).group(:date).count.transform_keys { |date| date.strftime('%Y-%m-%d') },
      total_connected: query.where(integration_field => true).distinct.count(:user_id),
      total_not_connected: query.where(integration_field => false).distinct.count(:user_id)
    }
  end

  def marketplace_apps_usage_report(start_date, end_date)
    query = base_usage_query(start_date, end_date)
    
    apps_data = query.where('number_of_marketplace_apps_installed > 0')
                     .pluck(:date, :number_of_marketplace_apps_installed, :name_of_marketplace_apps_installed)
    
    apps_by_date = {}
    app_names_frequency = Hash.new(0)
    
    apps_data.each do |date, count, app_names|
      date_str = date.strftime('%Y-%m-%d')
      apps_by_date[date_str] = (apps_by_date[date_str] || 0) + count
      
      app_names&.each { |app_name| app_names_frequency[app_name] += 1 }
    end
    
    {
      apps_installed_by_date: apps_by_date,
      most_popular_apps: app_names_frequency.sort_by { |_, count| -count }.first(10).to_h,
      total_apps_installed: query.sum(:number_of_marketplace_apps_installed)
    }
  end

  def calculate_comparison(period1_data, period2_data)
    return {} unless period1_data.is_a?(Hash) && period2_data.is_a?(Hash)

    comparison = {}
    
    # Handle different data structures
    if period1_data.keys.first&.match?(/\d{4}-\d{2}-\d{2}/) # Daily data
      comparison[:period1_total] = period1_data.values.sum
      comparison[:period2_total] = period2_data.values.sum
      comparison[:change] = comparison[:period2_total] - comparison[:period1_total]
      comparison[:percentage_change] = comparison[:period1_total] > 0 ? 
        ((comparison[:change].to_f / comparison[:period1_total]) * 100).round(2) : 0
    else # Summary data
      period1_data.each do |key, value1|
        value2 = period2_data[key] || 0
        next unless value1.is_a?(Numeric) && value2.is_a?(Numeric)
        
        change = value2 - value1
        percentage_change = value1 > 0 ? ((change.to_f / value1) * 100).round(2) : 0
        
        comparison[key] = {
          period1: value1,
          period2: value2,
          change: change,
          percentage_change: percentage_change
        }
      end
    end
    
    comparison
  end
end
