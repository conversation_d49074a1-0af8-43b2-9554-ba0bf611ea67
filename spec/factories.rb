# frozen_string_literal: true

FactoryBot.define do
  factory :usage_history do
    tenant
    user_id { rand(1..100) }
  end

  factory :usage do
    tenant
    user_id { rand(1..100) }
  end

  factory :account_detail_history do
    tenant
    email { Faker::Internet.unique.email }
    company { Faker::Company.unique.name }
    mobile { Faker::PhoneNumber.unique.cell_phone_in_e164 }
    industry { Faker::Company.unique.industry }
  end
  factory :account_detail do
    email { Faker::Internet.unique.email }
    company { Faker::Company.name }
    mobile { Faker::PhoneNumber.unique.cell_phone_in_e164 }
    tenant
  end

  factory :tenant do
    kylas_tenant_id { 123 }
    name { Faker::Name.unique.name }
  end

  factory :admin, class: 'User' do
    email { Faker::Internet.unique.email }
    name { Faker::Name.name }
    password { Faker::Internet.password(special_characters: true) }
    role { ADMIN }
  end

  factory :user, class: 'User' do
    email { Faker::Internet.unique.email }
    name { Faker::Name.name }
    password { Faker::Internet.password(special_characters: true) }
    role { USER }
  end

  factory :deactivated_user, class: 'User' do
    email { Faker::Internet.unique.email }
    name { Faker::Name.name }
    password { Faker::Internet.password(special_characters: true) }
    role { USER }
    deactivated { true }
  end

  factory :marketplace_app do
    rgs_input
    name { ["Apollo.io", "Arka Inventory", "Bulk Email Marketing"].sample }
    integrated { true }
  end

  factory :entity do
    rgs_input
    category { ENTITY_CATEGORY.sample }
    critical { true }
    frequency { ENTITY_FREQUENCY.sample }
    expected_volume { rand(10) }
  end

  factory :rgs_input do
    tenant
    total_users { rand(100) }
    total_managers { rand(100) }
  end

  factory :plan_detail do
    tenant
    name { ALLOWED_PLANS.sample }
    next_renewal { 1.month.from_now }
    ob_start { 1.week.ago }
    ob_completion { 2.days.from_now }
    add_on { "Extra features" }
    status { PLAN_STATUS.sample }
    last_updated_by_id { Faker::Number.unique.number }
  end

  factory :plan_detail_history do
    tenant
    name { ALLOWED_PLANS.sample }
    last_paid_on { Time.current }
    next_renewal { 1.month.from_now }
    ob_start { 1.week.ago }
    ob_completion { 2.days.from_now }
    add_on { "Extra features" }
    status { PLAN_STATUS.sample }
  end

  factory :customer_ask do
    tenant
  end

  factory :customer_requirement do
    customer_ask
    category { CUSTOMER_REQUIREMENT_CATEGORY.sample }
    status { CUSTOMER_REQUIREMENT_STATUS.sample }
    due_date { 1.month.from_now }
    description { "need voice calling" }
  end

  factory :usage do
    tenant
    user_id { Faker::Number.unique.number(digits: 6) }
    full_name { Faker::Name.name }
    email { Faker::Internet.email }
    plan_name { ALLOWED_PLANS.sample }
    status { 'active' }
    last_login_at { Faker::Time.between(from: 1.week.ago, to: Time.current) }
    active { true }
    verified { true }
    created_lead_count { rand(0..20) }
    created_deal_count { rand(0..10) }
    created_contact_count { rand(0..15) }
    updated_lead_count { rand(0..10) }
    updated_deal_count { rand(0..5) }
    updated_contact_count { rand(0..8) }
    created_task_count { rand(0..12) }
    created_note_count { rand(0..8) }
    created_meeting_count { rand(0..5) }
    created_company_count { rand(0..3) }
    updated_company_count { rand(0..2) }
    email_account_connected { [true, false].sample }
    connected_account_name { Faker::Internet.email }
    logged_in { [true, false].sample }
    calls_logged { rand(0..10) }
    emails_sent { rand(0..15) }
    date { Faker::Date.between(from: 1.month.ago, to: Date.current) }
    number_of_custom_dashboards_created { rand(0..3) }
    message_count { rand(0..20) }
    number_of_marketplace_apps_installed { rand(0..5) }
    name_of_marketplace_apps_installed { ['App1', 'App2', 'App3'].sample(rand(0..3)) }
    calendar_account_connected { [true, false].sample }
    connected_calendar_account_name { Faker::Internet.email }
    created_quote_count { rand(0..5) }
    updated_quote_count { rand(0..3) }
    primary_phone_number { Faker::PhoneNumber.phone_number }
    all_phone_numbers { [Faker::PhoneNumber.phone_number] }
    dau { [true, false].sample }
    kylas_tenant_id { Faker::Number.unique.number(digits: 4) }
    tenant_name { Faker::Company.name }
  end

  factory :usage_history do
    tenant
    user_id { Faker::Number.unique.number(digits: 6) }
    full_name { Faker::Name.name }
    email { Faker::Internet.email }
    plan_name { ALLOWED_PLANS.sample }
    status { 'active' }
    last_login_at { Faker::Time.between(from: 1.week.ago, to: Time.current) }
    active { true }
    verified { true }
    created_lead_count { rand(0..20) }
    created_deal_count { rand(0..10) }
    created_contact_count { rand(0..15) }
    updated_lead_count { rand(0..10) }
    updated_deal_count { rand(0..5) }
    updated_contact_count { rand(0..8) }
    created_task_count { rand(0..12) }
    created_note_count { rand(0..8) }
    created_meeting_count { rand(0..5) }
    created_company_count { rand(0..3) }
    updated_company_count { rand(0..2) }
    email_account_connected { [true, false].sample }
    connected_account_name { Faker::Internet.email }
    logged_in { [true, false].sample }
    calls_logged { rand(0..10) }
    emails_sent { rand(0..15) }
    date { Faker::Date.between(from: 1.month.ago, to: Date.current) }
    number_of_custom_dashboards_created { rand(0..3) }
    message_count { rand(0..20) }
    number_of_marketplace_apps_installed { rand(0..5) }
    name_of_marketplace_apps_installed { ['App1', 'App2', 'App3'].sample(rand(0..3)) }
    calendar_account_connected { [true, false].sample }
    connected_calendar_account_name { Faker::Internet.email }
    created_quote_count { rand(0..5) }
    updated_quote_count { rand(0..3) }
    primary_phone_number { Faker::PhoneNumber.phone_number }
    all_phone_numbers { [Faker::PhoneNumber.phone_number] }
    dau { [true, false].sample }
    kylas_tenant_id { Faker::Number.unique.number(digits: 4) }
    tenant_name { Faker::Company.name }
  end
end
