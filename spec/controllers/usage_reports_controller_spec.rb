# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UsageReportsController, type: :controller do
  include Devise::Test::ControllerHelpers
  let(:admin_user) { create(:admin) }
  let(:regular_user) { create(:user) }
  let(:tenant) { create(:tenant) }
  let(:account_detail) { create(:account_detail, tenant: tenant) }

  before do
    sign_in admin_user
  end

  describe 'GET #show' do
    it 'renders the usage reports page' do
      get :show, params: { id: account_detail.id }
      
      expect(response).to have_http_status(:success)
      expect(assigns(:report_types)).to be_present
      expect(assigns(:comparison_types)).to be_present
      expect(assigns(:date_ranges)).to be_present
    end

    it 'redirects when account detail not found' do
      get :show, params: { id: 999999 }
      
      expect(response).to redirect_to(account_details_path)
    end
  end

  describe 'GET #data' do
    let!(:usage_history) do
      create(:usage_history, 
             tenant: tenant, 
             date: Date.current,
             logged_in: true,
             created_lead_count: 5)
    end

    it 'returns report data as JSON' do
      get :data, params: { 
        id: account_detail.id,
        report_type: 'daily_logged_in_users',
        comparison_type: 'none',
        date_range: '7_days'
      }, format: :json
      
      expect(response).to have_http_status(:success)
      expect(JSON.parse(response.body)).to have_key('data')
    end

    it 'returns report data as HTML partial' do
      get :data, params: { 
        id: account_detail.id,
        report_type: 'daily_lead_creation',
        comparison_type: 'none',
        date_range: '7_days'
      }
      
      expect(response).to have_http_status(:success)
      expect(response.body).to include('report-data-container')
    end

    it 'handles comparison reports' do
      get :data, params: { 
        id: account_detail.id,
        report_type: 'daily_logged_in_users',
        comparison_type: 'yesterday_today',
        date_range: '7_days'
      }, format: :json
      
      expect(response).to have_http_status(:success)
      data = JSON.parse(response.body)
      expect(data).to have_key('period1')
      expect(data).to have_key('period2')
      expect(data).to have_key('comparison')
    end

    it 'handles custom date ranges' do
      get :data, params: { 
        id: account_detail.id,
        report_type: 'daily_logged_in_users',
        comparison_type: 'custom',
        start_date: 1.week.ago.to_date,
        end_date: Date.current
      }, format: :json
      
      expect(response).to have_http_status(:success)
    end

    it 'handles errors gracefully' do
      allow(UsageReportService).to receive(:new).and_raise(StandardError.new('Test error'))
      
      get :data, params: { 
        id: account_detail.id,
        report_type: 'daily_logged_in_users'
      }, format: :json
      
      expect(response).to have_http_status(:unprocessable_entity)
      expect(JSON.parse(response.body)).to have_key('error')
    end
  end

  describe 'GET #export' do
    let!(:usage_history) do
      create(:usage_history, 
             tenant: tenant, 
             date: Date.current,
             logged_in: true,
             created_lead_count: 5)
    end

    it 'exports report data as CSV' do
      get :export, params: { 
        id: account_detail.id,
        report_type: 'daily_logged_in_users',
        comparison_type: 'none',
        date_range: '7_days'
      }, format: :csv
      
      expect(response).to have_http_status(:success)
      expect(response.content_type).to eq('text/csv')
      expect(response.headers['Content-Disposition']).to include('attachment')
    end

    it 'handles export errors gracefully' do
      allow(UsageReportService).to receive(:new).and_raise(StandardError.new('Test error'))
      
      get :export, params: { 
        id: account_detail.id,
        report_type: 'daily_logged_in_users'
      }, format: :csv
      
      expect(response).to redirect_to(usage_reports_account_detail_path(account_detail))
    end
  end

  describe 'authentication and authorization' do
    it 'requires user authentication' do
      sign_out admin_user
      
      get :show, params: { id: account_detail.id }
      
      expect(response).to redirect_to(new_user_session_path)
    end

    it 'allows regular users to access reports' do
      sign_out admin_user
      sign_in regular_user
      
      get :show, params: { id: account_detail.id }
      
      expect(response).to have_http_status(:success)
    end

    it 'redirects deactivated users' do
      admin_user.update!(deactivated: true)
      
      get :show, params: { id: account_detail.id }
      
      expect(response).to redirect_to(new_user_session_path)
    end
  end
end
