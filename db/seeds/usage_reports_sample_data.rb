# frozen_string_literal: true

# Sample Data for Usage Reports Demo
# This file creates realistic sample data to demonstrate the usage reporting functionality

puts "🌱 Creating sample data for Usage Reports..."

# Create sample tenants if they don't exist
sample_tenants = []

3.times do |i|
  tenant = Tenant.find_or_create_by(kylas_tenant_id: 1000 + i) do |t|
    t.name = ["TechCorp Solutions", "Global Innovations Inc", "Digital Dynamics LLC"][i]
    t.system_updated_at = Time.current
  end
  sample_tenants << tenant
  puts "✅ Created tenant: #{tenant.name} (ID: #{tenant.kylas_tenant_id})"
end

# Create account details for each tenant
sample_tenants.each_with_index do |tenant, index|
  account_detail = AccountDetail.find_or_create_by(tenant: tenant) do |ad|
    ad.email = ["<EMAIL>", "<EMAIL>", "<EMAIL>"][index]
    ad.company = tenant.name
    ad.industry = ["Technology", "Manufacturing", "Consulting"][index]
    ad.mobile = ["******-0101", "******-0102", "******-0103"][index]
    ad.start_date = [3.months.ago, 6.months.ago, 1.year.ago][index]
    ad.kylas_tenant_id = tenant.kylas_tenant_id
    ad.tenant_name = tenant.name
    ad.profile_count = [15, 25, 40][index]
    ad.account_settings_completed = true
    ad.active_user_count = [12, 20, 35][index]
    ad.in_active_user_count = [3, 5, 5][index]
    ad.marketplace_apps_installed = [
      ["Kylas SMS", "Email Templates"],
      ["Kylas SMS", "Custom Fields", "Workflows"],
      ["Email Templates", "Custom Fields", "Workflows", "File Storage"]
    ][index]
    ad.last_updated_at = Time.current
  end
  puts "✅ Created account detail for: #{tenant.name}"
end

# Create comprehensive usage history data for the last 90 days
puts "\n📊 Generating usage history data for the last 90 days..."

sample_tenants.each_with_index do |tenant, tenant_index|
  user_count = [12, 20, 35][tenant_index]
  
  # Generate data for each day in the last 90 days
  (90.days.ago.to_date..Date.current).each do |date|
    # Create multiple user records per day to simulate real usage
    user_count.times do |user_index|
      # Simulate different user behavior patterns
      is_weekend = date.saturday? || date.sunday?
      is_active_user = user_index < (user_count * 0.8) # 80% active users
      is_power_user = user_index < (user_count * 0.3) # 30% power users
      
      # Calculate activity multipliers based on patterns
      weekend_multiplier = is_weekend ? 0.3 : 1.0
      active_multiplier = is_active_user ? 1.0 : 0.2
      power_multiplier = is_power_user ? 2.0 : 1.0
      
      # Add some randomness and trends
      days_ago = (Date.current - date).to_i
      growth_trend = 1.0 + (90 - days_ago) * 0.01 # Slight growth over time
      random_factor = 0.7 + rand * 0.6 # Random factor between 0.7 and 1.3
      
      base_multiplier = weekend_multiplier * active_multiplier * power_multiplier * growth_trend * random_factor
      
      usage_data = {
        tenant: tenant,
        user_id: 10000 + (tenant_index * 100) + user_index,
        full_name: "User #{user_index + 1} #{['Smith', 'Johnson', 'Williams', 'Brown', 'Jones'][user_index % 5]}",
        email: "user#{user_index + 1}@#{tenant.name.downcase.gsub(/[^a-z]/, '')}.com",
        plan_name: ["Embark", "Elevate", "Exceed"][tenant_index],
        status: is_active_user ? 'active' : 'inactive',
        last_login_at: is_active_user && !is_weekend ? date.beginning_of_day + rand(10).hours : nil,
        deactivated_at: is_active_user ? nil : date - rand(30).days,
        active: is_active_user,
        verified: true,
        logged_in: is_active_user && !is_weekend && rand < 0.8,
        dau: is_active_user && !is_weekend && rand < 0.6,
        date: date,
        kylas_tenant_id: tenant.kylas_tenant_id,
        tenant_name: tenant.name,
        
        # Activity metrics with realistic patterns
        created_lead_count: (rand(0..8) * base_multiplier).round,
        created_deal_count: (rand(0..3) * base_multiplier).round,
        created_contact_count: (rand(0..5) * base_multiplier).round,
        updated_lead_count: (rand(0..6) * base_multiplier).round,
        updated_deal_count: (rand(0..4) * base_multiplier).round,
        updated_contact_count: (rand(0..4) * base_multiplier).round,
        created_task_count: (rand(0..6) * base_multiplier).round,
        created_note_count: (rand(0..4) * base_multiplier).round,
        created_meeting_count: (rand(0..2) * base_multiplier).round,
        created_company_count: (rand(0..1) * base_multiplier).round,
        updated_company_count: (rand(0..1) * base_multiplier).round,
        created_quote_count: (rand(0..2) * base_multiplier).round,
        updated_quote_count: (rand(0..1) * base_multiplier).round,
        calls_logged: (rand(0..5) * base_multiplier).round,
        emails_sent: (rand(0..8) * base_multiplier).round,
        message_count: (rand(0..10) * base_multiplier).round,
        
        # Integration usage
        email_account_connected: is_power_user || rand < 0.4,
        connected_account_name: is_power_user || rand < 0.4 ? "<EMAIL>" : nil,
        calendar_account_connected: is_power_user || rand < 0.3,
        connected_calendar_account_name: is_power_user || rand < 0.3 ? "<EMAIL>" : nil,
        
        # Marketplace apps
        number_of_marketplace_apps_installed: is_power_user ? rand(2..5) : rand(0..2),
        name_of_marketplace_apps_installed: is_power_user ? 
          ["Kylas SMS", "Email Templates", "Custom Fields", "Workflows"].sample(rand(2..4)) :
          ["Kylas SMS"].sample(rand(0..1)),
        
        # Custom dashboards
        number_of_custom_dashboards_created: is_power_user ? rand(1..3) : 0,
        
        # Phone numbers
        primary_phone_number: "******-#{sprintf('%04d', user_index)}",
        all_phone_numbers: ["******-#{sprintf('%04d', user_index)}"]
      }
      
      UsageHistory.find_or_create_by(
        tenant: tenant,
        user_id: usage_data[:user_id],
        date: date
      ) do |uh|
        usage_data.each { |key, value| uh.send("#{key}=", value) if uh.respond_to?("#{key}=") }
      end
    end
  end
  
  puts "✅ Generated usage data for #{tenant.name} (#{user_count} users × 90 days)"
end

# Create some customer asks for demonstration
puts "\n💬 Creating sample customer asks and requirements..."

sample_tenants.each_with_index do |tenant, index|
  customer_ask = CustomerAsk.find_or_create_by(tenant: tenant)
  
  # Create sample requirements
  sample_requirements = [
    {
      category: 'LEAD',
      status: 'IN_PROGRESS',
      description: 'Need ability to import leads from LinkedIn Sales Navigator'
    },
    {
      category: 'AUTOMATION',
      status: 'COMPLETED',
      description: 'Automated email sequences for lead nurturing'
    },
    {
      category: 'INTEGRATION',
      status: 'PENDING',
      description: 'Integration with Slack for team notifications'
    },
    {
      category: 'CUSTOMISATION',
      status: 'ACCEPTED',
      description: 'Custom fields for industry-specific data capture'
    }
  ]
  
  sample_requirements.each do |req_data|
    CustomerRequirement.find_or_create_by(
      customer_ask: customer_ask,
      category: req_data[:category],
      description: req_data[:description]
    ) do |cr|
      cr.status = req_data[:status]
    end
  end
  
  puts "✅ Created customer requirements for #{tenant.name}"
end

# Create RGS inputs for demonstration
puts "\n📈 Creating sample RGS inputs..."

sample_tenants.each_with_index do |tenant, index|
  rgs_input = RgsInput.find_or_create_by(tenant: tenant) do |rgs|
    rgs.total_users = [15, 25, 40][index]
    rgs.total_managers = [3, 5, 8][index]
  end
  
  # Create sample entities
  entity_data = [
    { category: 'LEAD', critical: true, frequency: 'DAILY', expected_volume: 50 },
    { category: 'DEAL', critical: true, frequency: 'DAILY', expected_volume: 20 },
    { category: 'CONTACT', critical: false, frequency: 'DAILY', expected_volume: 30 },
    { category: 'COMPANY', critical: false, frequency: 'DAILY', expected_volume: 10 }
  ]
  
  entity_data.each do |entity_attrs|
    Entity.find_or_create_by(
      rgs_input: rgs_input,
      category: entity_attrs[:category]
    ) do |entity|
      entity.critical = entity_attrs[:critical]
      entity.frequency = entity_attrs[:frequency]
      entity.expected_volume = entity_attrs[:expected_volume] * (index + 1)
    end
  end
  
  # Create marketplace apps
  app_data = [
    { name: 'Kylas SMS', integrated: true },
    { name: 'Email Templates', integrated: true },
    { name: 'Custom Fields', integrated: index > 0 },
    { name: 'Workflows', integrated: index > 1 }
  ]
  
  app_data.each do |app_attrs|
    MarketplaceApp.find_or_create_by(
      rgs_input: rgs_input,
      name: app_attrs[:name]
    ) do |app|
      app.integrated = app_attrs[:integrated]
    end
  end
  
  puts "✅ Created RGS inputs for #{tenant.name}"
end

puts "\n🎉 Sample data creation completed!"
puts "\n📋 Summary:"
puts "   • #{sample_tenants.count} sample tenants created"
puts "   • #{AccountDetail.count} account details created"
puts "   • #{UsageHistory.count} usage history records created"
puts "   • #{CustomerAsk.count} customer asks created"
puts "   • #{CustomerRequirement.count} customer requirements created"
puts "   • #{RgsInput.count} RGS inputs created"
puts "   • #{Entity.count} entities created"
puts "   • #{MarketplaceApp.count} marketplace apps created"

puts "\n🚀 You can now:"
puts "   1. Navigate to any account detail page"
puts "   2. Click on the 'Usage Reports' tab"
puts "   3. Try different report types and comparison options"
puts "   4. Export reports as CSV files"
puts "\n💡 Recommended reports to try:"
puts "   • Daily Logged In Users with Yesterday vs Today comparison"
puts "   • Daily Lead Creation with Last Month vs This Month comparison"
puts "   • User Activity Summary with Last Quarter vs This Quarter comparison"
puts "   • Feature Usage Overview with no comparison"
