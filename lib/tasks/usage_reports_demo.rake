# frozen_string_literal: true

namespace :usage_reports do
  desc "Generate sample data for usage reports demonstration"
  task demo_data: :environment do
    puts "🎯 Starting Usage Reports Demo Data Generation..."
    puts "=" * 60
    
    # Load and execute the sample data script
    load Rails.root.join('db', 'seeds', 'usage_reports_sample_data.rb')
    
    puts "=" * 60
    puts "✅ Demo data generation completed successfully!"
    puts "\n🎮 How to test the Usage Reports feature:"
    puts "\n1. Start your Rails server:"
    puts "   rails server"
    puts "\n2. Login to the application"
    puts "\n3. Navigate to any account (you'll see 3 sample accounts):"
    puts "   • TechCorp Solutions"
    puts "   • Global Innovations Inc"
    puts "   • Digital Dynamics LLC"
    puts "\n4. Click on the 'Usage Reports' tab"
    puts "\n5. Try these sample reports:"
    
    sample_reports = [
      {
        name: "Daily Logged In Users",
        comparison: "Yesterday vs Today",
        description: "See login patterns comparison"
      },
      {
        name: "Daily Lead Creation",
        comparison: "Last Month vs This Month",
        description: "Compare lead generation trends"
      },
      {
        name: "User Activity Summary",
        comparison: "Last Quarter vs This Quarter",
        description: "Overall user engagement analysis"
      },
      {
        name: "Feature Usage Overview",
        comparison: "No Comparison",
        description: "Complete feature utilization overview"
      },
      {
        name: "Daily Deal Creation",
        comparison: "Last Week vs This Week",
        description: "Deal creation patterns"
      },
      {
        name: "Marketplace Apps Usage",
        comparison: "Last Month vs This Month",
        description: "App adoption and usage trends"
      }
    ]
    
    sample_reports.each_with_index do |report, index|
      puts "\n   #{index + 1}. #{report[:name]}"
      puts "      Comparison: #{report[:comparison]}"
      puts "      Purpose: #{report[:description]}"
    end
    
    puts "\n📊 Data Highlights:"
    puts "   • 90 days of historical usage data"
    puts "   • Realistic user behavior patterns"
    puts "   • Weekend vs weekday activity differences"
    puts "   • Growth trends over time"
    puts "   • Different user types (active, power users, inactive)"
    puts "   • Multiple tenants with varying sizes"
    
    puts "\n🔧 Advanced Features to Test:"
    puts "   • Custom date range selection"
    puts "   • CSV export functionality"
    puts "   • Interactive charts and visualizations"
    puts "   • Responsive design on different screen sizes"
    puts "   • Real-time report generation"
    
    puts "\n💡 Pro Tips:"
    puts "   • Try different comparison types to see trends"
    puts "   • Export reports as CSV for external analysis"
    puts "   • Compare different tenants to see usage patterns"
    puts "   • Use custom date ranges for specific periods"
    
    puts "\n🎉 Happy testing!"
  end

  desc "Clean up demo data (removes sample tenants and related data)"
  task clean_demo_data: :environment do
    puts "🧹 Cleaning up Usage Reports demo data..."
    
    # Find demo tenants by their kylas_tenant_id range
    demo_tenants = Tenant.where(kylas_tenant_id: 1000..1002)
    
    if demo_tenants.any?
      puts "Found #{demo_tenants.count} demo tenants to clean up:"
      demo_tenants.each { |t| puts "  • #{t.name} (ID: #{t.kylas_tenant_id})" }
      
      # Clean up related data
      demo_tenants.each do |tenant|
        puts "Cleaning up data for #{tenant.name}..."
        
        # Clean up usage histories
        usage_count = tenant.usage_histories.count
        tenant.usage_histories.destroy_all
        puts "  ✅ Removed #{usage_count} usage history records"
        
        # Clean up customer asks and requirements
        if tenant.customer_ask
          req_count = tenant.customer_ask.customer_requirements.count
          tenant.customer_ask.customer_requirements.destroy_all
          tenant.customer_ask.destroy
          puts "  ✅ Removed customer ask with #{req_count} requirements"
        end
        
        # Clean up RGS inputs
        if tenant.rgs_input
          entity_count = tenant.rgs_input.entities.count
          app_count = tenant.rgs_input.marketplace_apps.count
          tenant.rgs_input.entities.destroy_all
          tenant.rgs_input.marketplace_apps.destroy_all
          tenant.rgs_input.destroy
          puts "  ✅ Removed RGS input with #{entity_count} entities and #{app_count} apps"
        end
        
        # Clean up account detail
        if tenant.account_detail
          tenant.account_detail.destroy
          puts "  ✅ Removed account detail"
        end
        
        # Clean up account detail histories
        history_count = tenant.account_detail_history.count
        tenant.account_detail_history.destroy_all
        puts "  ✅ Removed #{history_count} account detail histories"
        
        # Finally, remove the tenant
        tenant.destroy
        puts "  ✅ Removed tenant: #{tenant.name}"
      end
      
      puts "\n🎉 Demo data cleanup completed!"
    else
      puts "No demo data found to clean up."
    end
  end

  desc "Show demo data statistics"
  task stats: :environment do
    puts "📊 Usage Reports Demo Data Statistics"
    puts "=" * 50
    
    demo_tenants = Tenant.where(kylas_tenant_id: 1000..1002)
    
    if demo_tenants.any?
      puts "Demo Tenants: #{demo_tenants.count}"
      
      demo_tenants.each do |tenant|
        puts "\n🏢 #{tenant.name} (ID: #{tenant.kylas_tenant_id})"
        puts "   Usage History Records: #{tenant.usage_histories.count}"
        puts "   Date Range: #{tenant.usage_histories.minimum(:date)} to #{tenant.usage_histories.maximum(:date)}"
        puts "   Unique Users: #{tenant.usage_histories.distinct.count(:user_id)}"
        puts "   Total Leads Created: #{tenant.usage_histories.sum(:created_lead_count)}"
        puts "   Total Deals Created: #{tenant.usage_histories.sum(:created_deal_count)}"
        puts "   Total Contacts Created: #{tenant.usage_histories.sum(:created_contact_count)}"
        puts "   Customer Requirements: #{tenant.customer_ask&.customer_requirements&.count || 0}"
        puts "   RGS Entities: #{tenant.rgs_input&.entities&.count || 0}"
        puts "   Marketplace Apps: #{tenant.rgs_input&.marketplace_apps&.count || 0}"
      end
      
      puts "\n📈 Overall Statistics:"
      puts "   Total Usage Records: #{UsageHistory.joins(:tenant).where(tenants: { kylas_tenant_id: 1000..1002 }).count}"
      puts "   Total Customer Requirements: #{CustomerRequirement.joins(customer_ask: :tenant).where(tenants: { kylas_tenant_id: 1000..1002 }).count}"
      puts "   Total RGS Entities: #{Entity.joins(rgs_input: :tenant).where(tenants: { kylas_tenant_id: 1000..1002 }).count}"
      puts "   Total Marketplace Apps: #{MarketplaceApp.joins(rgs_input: :tenant).where(tenants: { kylas_tenant_id: 1000..1002 }).count}"
      
    else
      puts "No demo data found. Run 'rake usage_reports:demo_data' to generate sample data."
    end
  end

  desc "Generate additional usage data for a specific date range"
  task :generate_data, [:start_date, :end_date, :tenant_id] => :environment do |t, args|
    start_date = args[:start_date] ? Date.parse(args[:start_date]) : 30.days.ago.to_date
    end_date = args[:end_date] ? Date.parse(args[:end_date]) : Date.current
    tenant_id = args[:tenant_id]&.to_i
    
    puts "📅 Generating additional usage data..."
    puts "Date Range: #{start_date} to #{end_date}"
    
    tenants = tenant_id ? [Tenant.find(tenant_id)] : Tenant.where(kylas_tenant_id: 1000..1002)
    
    if tenants.empty?
      puts "❌ No tenants found. Please run 'rake usage_reports:demo_data' first."
      exit
    end
    
    tenants.each do |tenant|
      puts "Generating data for #{tenant.name}..."
      user_count = tenant.account_detail&.active_user_count || 15
      
      (start_date..end_date).each do |date|
        user_count.times do |user_index|
          # Skip if record already exists
          next if UsageHistory.exists?(tenant: tenant, user_id: 10000 + user_index, date: date)
          
          # Generate realistic usage data (simplified version)
          is_weekend = date.saturday? || date.sunday?
          base_activity = is_weekend ? 0.3 : 1.0
          
          UsageHistory.create!(
            tenant: tenant,
            user_id: 10000 + user_index,
            full_name: "User #{user_index + 1}",
            email: "user#{user_index + 1}@#{tenant.name.downcase.gsub(/[^a-z]/, '')}.com",
            plan_name: "Elevate",
            status: 'active',
            active: true,
            verified: true,
            logged_in: !is_weekend && rand < 0.8,
            dau: !is_weekend && rand < 0.6,
            date: date,
            kylas_tenant_id: tenant.kylas_tenant_id,
            tenant_name: tenant.name,
            created_lead_count: (rand(0..8) * base_activity).round,
            created_deal_count: (rand(0..3) * base_activity).round,
            created_contact_count: (rand(0..5) * base_activity).round,
            calls_logged: (rand(0..5) * base_activity).round,
            emails_sent: (rand(0..8) * base_activity).round
          )
        end
      end
      
      puts "✅ Generated data for #{tenant.name}"
    end
    
    puts "🎉 Additional data generation completed!"
  end
end
